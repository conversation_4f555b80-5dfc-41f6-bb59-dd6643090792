using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using YunEdu.Common;

namespace ECB.PC.Web.ECB.PC
{
    public partial class custom_working_list : YunEdu.Authority.AdminCommonJC
    {
		YunEdu.BLL.UserInfos bllUserinfos = new YunEdu.BLL.UserInfos();
		protected void Page_Load(object sender, EventArgs e)
        {
            Area_Basic.DefaultColumnPath = modelAreaUser.ColumnPath;
            if (!IsPostBack)
            {
                // 绑定数据
                Bind(true); 
            }
        }

        /// <summary>
        /// 绑定工作记录数据
        /// </summary>
        private void Bind(bool isSearch)
        {
            try
            {
                this.txtPageNum.Text = this.AspNetPager1.PageSize.ToString();
                
                StringBuilder sbWhere = new StringBuilder();
                sbWhere.Append(" 1=1 ");
                
                // 地区筛选
                if (Area_Basic.CurrentColumnId.HasValue)
                {
                    sbWhere.AppendFormat(" AND r.ColumnId={0} ", Area_Basic.CurrentColumnId.Value);
                }
                
                // 时间筛选
                DateTime startDate, endDate;
                if (DateTime.TryParse(txtStartDate.Text, out startDate))
                {
                    sbWhere.AppendFormat(" AND r.BeginDate >= '{0}' ", startDate.ToString("yyyy-MM-dd"));
                }
                if (DateTime.TryParse(txtEndDate.Text, out endDate))
                {
                    endDate = endDate.AddDays(1);
                    sbWhere.AppendFormat(" AND r.EndDate < '{0}' ", endDate.ToString("yyyy-MM-dd"));
                }
                
                // 关键字筛选
                if (!string.IsNullOrEmpty(txtKeyword.Text.Trim()))
                {
                    sbWhere.AppendFormat(" AND m.CName LIKE '%{0}%' ", txtKeyword.Text.Trim());
                }

                
                string where = sbWhere.ToString();
                string tbName = @"custom_Working_record r 
                                LEFT JOIN aspnet_Membership m ON r.UserId = m.UserId 
                                LEFT JOIN UserInfos u ON r.UserId = u.UserID";
                string fields = "r.*, m.CName as TeacherName, m.IDCardNo";
                string order = "r.BeginDate DESC, m.CName";
                
                // 设置分页参数
                int pageSize = 20;
                int.TryParse(txtPageNum.Text, out pageSize);
                if(pageSize <= 0) pageSize = 20;
                int pageIndex = AspNetPager1.CurrentPageIndex;
                
                // 查询数据
                AspNetPager1.RecordCount = GetRecordByPageOrder.GetCount(tbName, where);
                DataSet ds = GetRecordByPageOrder.GetList(tbName, pageSize, pageIndex, fields, where, order);
                
                // 绑定数据到GridView
                gvData.DataSource = ds;
                gvData.DataBind();
            }
            catch (Exception ex)
            {
                YunEdu.Common.MessageBox.ResponseScript(this, $"showMessage('false','数据加载失败：{ex.Message}',2)");
            }
        }

        /// <summary>
        /// 查询按钮点击事件
        /// </summary>
        protected void btnSearch_Click(object sender, EventArgs e)
        {
            AspNetPager1.CurrentPageIndex = 1;
            Bind(true);
        }

        /// <summary>
        /// 分页事件
        /// </summary>
        protected void AspNetPager1_PageChanged(object sender, EventArgs e)
        {
            Bind(false);
        }

        /// <summary>
        /// 每页显示数量改变事件
        /// </summary>
        protected void txtPageNum_TextChanged(object sender, EventArgs e)
        {
            int pageSize = 20;
            if (int.TryParse(txtPageNum.Text, out pageSize) && pageSize > 0)
            {
                AspNetPager1.PageSize = pageSize;
                AspNetPager1.CurrentPageIndex = 1;
                Bind(true);
            }
            else
            {
                txtPageNum.Text = AspNetPager1.PageSize.ToString();
            }
        }

        /// <summary>
        /// 删除单条记录
        /// </summary>
        protected void lbtnDelete_Command(object sender, CommandEventArgs e)
        {
            try
            {
                string recordId = e.CommandArgument.ToString();
                if (!string.IsNullOrEmpty(recordId))
                {
                    Guid id = new Guid(recordId);
                    
                    BLL.custom_Working_record bll = new BLL.custom_Working_record();
                    if (bll.Delete(id))
                    {
                        ScriptManager.RegisterStartupScript(this, this.GetType(), "success", "layer.msg('删除成功！', {icon: 1}); setTimeout(function(){location.reload();}, 1000);", true);
                    }
                    else
                    {
                        ScriptManager.RegisterStartupScript(this, this.GetType(), "error", "layer.msg('删除失败！', {icon: 2});", true);
                    }
                }
            }
            catch (Exception ex)
            {
                ScriptManager.RegisterStartupScript(this, this.GetType(), "error", $"layer.msg('删除失败：{ex.Message}', {{icon: 2}});", true);
            }
        }

        /// <summary>
        /// 删除选中记录
        /// </summary>
        protected void btnDelSelect_Click(object sender, EventArgs e)
        {
            try
            {
                BLL.custom_Working_record bll = new BLL.custom_Working_record();
                bool hasDeleted = false;

                foreach (GridViewRow item in this.gvData.Rows)
                {
                    CheckBox ckb = (CheckBox)item.FindControl("chkItem1");
                    if (ckb != null && ckb.Checked)
                    {
                        Guid Id = Guid.Parse(gvData.DataKeys[item.RowIndex][0].ToString());
                        if (bll.Delete(Id))
                        {
                            hasDeleted = true;
                        }
                    }
                }

                if (hasDeleted)
                {
                    YunEdu.Common.MessageBox.ResponseScript(this, "showMessage('true','删除成功！',1)");
                    Bind(true);
                }
                else
                {
                    YunEdu.Common.MessageBox.ResponseScript(this, "showMessage('false','删除失败！',2)");
                }
            }
            catch (Exception ex)
            {
                YunEdu.Common.MessageBox.ResponseScript(this, $"showMessage('false','删除失败：{ex.Message}',2)");
            }
        }

        /// <summary>
        /// 导出Excel
        /// </summary>
        protected void btnExport_Click(object sender, EventArgs e)
        {
            try
            {
                // 获取所有数据（不分页）
                StringBuilder sbWhere = new StringBuilder();
                sbWhere.Append(" 1=1 ");
                
                // 地区筛选
                if (Area_Basic.CurrentColumnId.HasValue)
                {
                    sbWhere.AppendFormat(" AND r.ColumnId={0} ", Area_Basic.CurrentColumnId.Value);
                }
                
                // 时间筛选
                DateTime startDate, endDate;
                if (DateTime.TryParse(txtStartDate.Text, out startDate))
                {
                    sbWhere.AppendFormat(" AND r.BeginDate >= '{0}' ", startDate.ToString("yyyy-MM-dd"));
                }
                if (DateTime.TryParse(txtEndDate.Text, out endDate))
                {
                    endDate = endDate.AddDays(1);
                    sbWhere.AppendFormat(" AND r.EndDate < '{0}' ", endDate.ToString("yyyy-MM-dd"));
                }
                
                // 关键字筛选
                if (!string.IsNullOrEmpty(txtKeyword.Text.Trim()))
                {
                    sbWhere.AppendFormat(" AND m.CName LIKE '%{0}%' ", txtKeyword.Text.Trim());
                }


                string sql = $@"SELECT m.CName as '教师姓名', m.IDCardNo as '身份证号',
                              r.WorkName as '工作名称',
                              r.WorkContent as '工作内容',
                              CONVERT(varchar(10), r.BeginDate, 120) as '开始日期',
                              CONVERT(varchar(10), r.EndDate, 120) as '结束日期',
                              r.Dept as '部门',
                              r.DeptType as '部门类别',
                              r.WeekNum as '周课时数',
                              r.Mark as '备注'
                              FROM custom_Working_record r
                              LEFT JOIN aspnet_Membership m ON r.UserId = m.UserId
                              LEFT JOIN UserInfos u ON r.UserId = u.UserID
                              WHERE {sbWhere}
                              ORDER BY r.BeginDate DESC, m.CName";

                DataTable dt = YunEdu.DBUtility.DbHelperSQL.Query(sql).Tables[0];

                if (dt.Rows.Count > 0)
                {
					// 导出Excel
					string fileName = $"教师工作量记录_{DateTime.Now:yyyyMMddHHmmss}.xls";
					string _filePath = CodeTable.GetDirectoryTemp("1", UserName, CodeTable.FileType.files) + fileName;
					string strRootPath = Server.MapPath("/");
					YunEdu.Common.DataToExcel.ExportEasy(dt, "", _filePath, strRootPath);
					Response.ContentType = "application/x-zip-compressed";
					Response.AddHeader("Content-Disposition", "attachment;filename=" + Server.UrlEncode("教师工作量记录.xls"));
					string filename = Server.MapPath(_filePath);
					Response.TransmitFile(filename);
				}
                else
                {
                    YunEdu.Common.MessageBox.ResponseScript(this, "showMessage('false','没有数据可导出！',2)");
                }
            }
            catch (Exception ex)
            {
                YunEdu.Common.MessageBox.ResponseScript(this, $"showMessage('false','导出失败：{ex.Message}',2)");
            }
        }

        /// <summary>
        /// 下载Excel模板
        /// </summary>
        protected void btnDownloadTemplate_Click(object sender, EventArgs e)
        {
			Response.Clear();
			Response.ContentType = "application/x-zip-compressed";
			Response.AddHeader("Content-Disposition", "attachment;filename=" +HttpUtility.UrlEncode("教师工作量模板.xls", System.Text.Encoding.UTF8));
			string filePath = Server.MapPath("/ECB.PC/template/教师工作量模板.xls");
			Response.TransmitFile(filePath);
        }

        /// <summary>
        /// 导入工作记录
        /// </summary>
        protected void btnImport_Click(object sender, EventArgs e)
        {
            string errCode = "";
            try
            {
                // 处理Excel文件上传
                if (fileExcel.HasFile)
                {
                    try
                    {
                        UpLoad.UploadFileInfo fileInfo = new UpLoad().UpLoadFileTemp(fileExcel, modelAreaUser.ColumnPath, UserName, CodeTable.FileType.files);

                        // 获取Excel数据
                        int errorCode = 0;
                        DataTable dt = YunEdu.Common.DataToExcel.getExcelData(fileInfo.AbsolutePath, out errorCode);
                        errCode = errorCode.ToString();

                        if (dt != null && dt.Rows.Count > 0)
                        {
                            // 验证数据
                            if (!dt.Columns.Contains("身份证号码") || !dt.Columns.Contains("工作名称") ||
                                !dt.Columns.Contains("工作内容") || !dt.Columns.Contains("开始日期") ||
                                !dt.Columns.Contains("结束日期") || !dt.Columns.Contains("部门") ||
                                !dt.Columns.Contains("部门类别") || !dt.Columns.Contains("周课时数") || !dt.Columns.Contains("备注"))
                            {
                                throw new Exception("Excel文件格式错误，必须包含【身份证号码】、【工作名称】、【工作内容】、【开始日期】、【结束日期】、【部门】、【部门类别】、【周课时数】和【备注】列");
                            }

                            // 创建事务列表
                            ArrayList sqlList = new ArrayList();
                            BLL.custom_Working_record bll = new BLL.custom_Working_record();

                            // 处理Excel数据并添加到事务列表
                            ProcessExcelData(dt, sqlList);

                            // 执行事务
                            if (bll.ExecuteSqlTran(sqlList))
                            {
                                YunEdu.Common.MessageBox.ResponseScript(this, "showMessage('true','导入成功！',1)");
                                Bind(true);
                            }
                            else
                            {
                                YunEdu.Common.MessageBox.ResponseScript(this, "showMessage('false','保存数据失败',2)");
                            }
                        }
                        else
                        {
                            YunEdu.Common.MessageBox.ResponseScript(this, $"showMessage('false','Excel文件解析失败：{errCode}',2)");
                        }
                    }
                    catch (Exception ex)
                    {
                        YunEdu.Common.MessageBox.ResponseScript(this, $"showMessage('false','处理Excel数据失败：{ex.Message}',2)");
                    }
                }
                else
                {
                    YunEdu.Common.MessageBox.ResponseScript(this, "showMessage('false','请选择Excel文件',2)");
                }
            }
            catch (Exception ex)
            {
                YunEdu.Common.MessageBox.ResponseScript(this, $"showMessage('false','操作失败：{ex.Message}',2)");
            }
        }

        /// <summary>
        /// 处理Excel数据并生成SQL语句
        /// </summary>
        private void ProcessExcelData(DataTable dt, ArrayList sqlList)
        {
            // 先验证所有数据，如果有错误则不导入
            List<string> errorMessages = ValidateExcelData(dt);
            if (errorMessages.Count > 0)
            {
                string errorMsg = "数据验证失败，请修正以下错误后重新导入：\\n" + string.Join("\\n", errorMessages);
                throw new Exception(errorMsg);
            }

            // 验证通过后，处理数据
            BLL.custom_Working_record bll = new BLL.custom_Working_record();
            int rowIndex = 1; // 从第1行开始（不包括标题行）

            foreach (DataRow row in dt.Rows)
            {
                rowIndex++;

                string idCardNo = row["身份证号码"]?.ToString()?.Trim();
                string workName = row["工作名称"]?.ToString()?.Trim();
                string workContent = row["工作内容"]?.ToString()?.Trim();
                string beginDateStr = row["开始日期"]?.ToString()?.Trim();
                string endDateStr = row["结束日期"]?.ToString()?.Trim();
                string dept = row["部门"]?.ToString()?.Trim();
                string deptType = row["部门类别"]?.ToString()?.Trim();
                string weekNumStr = row["周课时数"]?.ToString()?.Trim();
                string mark = row["备注"]?.ToString()?.Trim();

                // 解析时间和数字
                DateTime beginDate = DateTime.Parse(beginDateStr);
                DateTime endDate = DateTime.Parse(endDateStr);
                int weekNum = int.Parse(weekNumStr);

				Guid teacherId = bllUserinfos.GetModelByNewCardId(idCardNo).UserID;

				// 创建工作记录
				Model.custom_Working_record model = new Model.custom_Working_record();
                model.ID = Guid.NewGuid();
                model.ColumnId = modelAreaUser.ColumnID;
                model.ColumnPath = modelAreaUser.ColumnPath;
                model.UserId = teacherId;
                model.WorkName = workName ?? "";
                model.WorkContent = workContent ?? "";
                model.BeginDate = beginDate;
                model.EndDate = endDate;
                model.Dept = dept ?? "";
                model.DeptType = deptType ?? "";
                model.WeekNum = weekNum;
                model.Mark = mark ?? "";
                model.Createtime = DateTime.Now;
                model.Creator = new Guid(UserId);

                // 添加到事务列表
                bll.Add(model, sqlList);
            }
        }

        /// <summary>
        /// 验证Excel数据
        /// </summary>
        private List<string> ValidateExcelData(DataTable dt)
        {
            List<string> errorMessages = new List<string>();
            int rowIndex = 1; // 从第1行开始（不包括标题行）

            foreach (DataRow row in dt.Rows)
            {
                rowIndex++;
                List<string> rowErrors = new List<string>();

                try
                {
                    string idCardNo = row["身份证号码"]?.ToString()?.Trim();
                    string workName = row["工作名称"]?.ToString()?.Trim();
                    string workContent = row["工作内容"]?.ToString()?.Trim();
                    string beginDateStr = row["开始日期"]?.ToString()?.Trim();
                    string endDateStr = row["结束日期"]?.ToString()?.Trim();
                    string dept = row["部门"]?.ToString()?.Trim();
                    string deptType = row["部门类别"]?.ToString()?.Trim();
                    string weekNumStr = row["周课时数"]?.ToString()?.Trim();

                    // 验证身份证号
                    if (string.IsNullOrEmpty(idCardNo))
                    {
                        rowErrors.Add("身份证号码不能为空");
                    }
                    else
                    {
						// 验证教师是否存在
						YunEdu.Model.UserInfos model = bllUserinfos.GetModelByNewCardId(idCardNo);
						if (model == null)
						{
							rowErrors.Add($"身份证号码[{idCardNo}]找不到对应的教师");
						}
					}

                    // 验证工作名称
                    if (string.IsNullOrEmpty(workName))
                    {
                        rowErrors.Add("工作名称不能为空");
                    }

                    // 验证工作内容
                    if (string.IsNullOrEmpty(workContent))
                    {
                        rowErrors.Add("工作内容不能为空");
                    }

                    // 验证开始日期
                    if (string.IsNullOrEmpty(beginDateStr))
                    {
                        rowErrors.Add("开始日期不能为空");
                    }
                    else
                    {
                        DateTime beginDate;
                        if (!DateTime.TryParse(beginDateStr, out beginDate))
                        {
                            rowErrors.Add($"开始日期[{beginDateStr}]格式错误，请使用yyyy-MM-dd格式");
                        }
                    }

                    // 验证结束日期
                    if (string.IsNullOrEmpty(endDateStr))
                    {
                        rowErrors.Add("结束日期不能为空");
                    }
                    else
                    {
                        DateTime endDate;
                        if (!DateTime.TryParse(endDateStr, out endDate))
                        {
                            rowErrors.Add($"结束日期[{endDateStr}]格式错误，请使用yyyy-MM-dd格式");
                        }
                        else
                        {
                            // 验证时间逻辑
                            DateTime beginDate;
                            if (DateTime.TryParse(beginDateStr, out beginDate) && endDate <= beginDate)
                            {
                                rowErrors.Add("结束日期必须大于开始日期");
                            }
                        }
                    }

                    // 验证部门
                    if (string.IsNullOrEmpty(dept))
                    {
                        rowErrors.Add("部门不能为空");
                    }

                    // 验证部门类别
                    if (string.IsNullOrEmpty(deptType))
                    {
                        rowErrors.Add("部门类别不能为空");
                    }

                    // 验证周课时数
                    if (string.IsNullOrEmpty(weekNumStr))
                    {
                        rowErrors.Add("周课时数不能为空");
                    }
                    else
                    {
                        int weekNum;
                        if (!int.TryParse(weekNumStr, out weekNum))
                        {
                            rowErrors.Add($"周课时数[{weekNumStr}]格式错误，请输入整数");
                        }
                        else if (weekNum < 0)
                        {
                            rowErrors.Add("周课时数不能为负数");
                        }
                    }

                    // 如果该行有错误，添加到错误列表
                    if (rowErrors.Count > 0)
                    {
                        errorMessages.Add($"第{rowIndex}行：{string.Join("；", rowErrors)}");
                    }
                }
                catch (Exception ex)
                {
                    errorMessages.Add($"第{rowIndex}行：数据处理异常 - {ex.Message}");
                }
            }

            return errorMessages;
        }
    }
}
