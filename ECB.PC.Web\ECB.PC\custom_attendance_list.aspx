﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="custom_attendance_list.aspx.cs" Inherits="ECB.PC.Web.ECB.PC.custom_attendance_list" StylesheetTheme="Admin_Default" EnableEventValidation="false" %>

<%@ Register Assembly="XYArea" Namespace="XYArea.Basic" TagPrefix="cc3" %>
<%@ Register Assembly="AspNetPager" Namespace="Wuqi.Webdiyer" TagPrefix="webdiyer" %>
<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<link href="/js/layer/skin/default/layer.css" rel="stylesheet" />
	<script src="/js/jquery-1.8.3.min.js"></script>
	<script src="/js/My97DatePicker/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
	<script src="/admin/js/Common.js"></script>
	<script src="/js/layer/layer.js"></script>
	<title>教师考勤记录管理</title>
	<style>
		.btn_Caozuo { text-decoration: none; color: #0D638F; cursor: pointer; }
		.action-btn {
			display: inline-block;
			margin: 0 3px;
			text-decoration: none;
		}

	</style>
</head>
<body>
	<form id="form1" runat="server">
		<div class="container">
			<div class="search-bar">
				<cc3:Area_Basic ID="Area_Basic" runat="server"
					Style="display: inline-block; vertical-align: middle; margin-bottom: 3px;" />
				创建时间：
                <asp:TextBox ID="txtStartDate" runat="server" CssClass="Wdate" Width="100px" onclick="WdatePicker({dateFmt:'yyyy-MM-dd'})"></asp:TextBox>
                至
                <asp:TextBox ID="txtEndDate" runat="server" CssClass="Wdate" Width="100px" onclick="WdatePicker({dateFmt:'yyyy-MM-dd'})"></asp:TextBox>
				<asp:TextBox ID="txtKeyword" runat="server" placeholder="标题" Width="150px"></asp:TextBox>
				<asp:Button ID="btnSearch" runat="server" Text="查 询" ValidationGroup="1" CssClass="btn" OnClick="btnSearch_Click" />
			</div>
			<asp:GridView ID="gvInfo" runat="server" DataKeyNames="ID" AutoGenerateColumns="False" EmptyDataText="暂无数据!" RowStyle-HorizontalAlign="Center" OnRowDataBound="gvInfo_RowDataBound">
				<Columns>
					<asp:BoundField HeaderText="标题" DataField="Title" ItemStyle-HorizontalAlign="left" />
					<asp:BoundField HeaderText="学校" DataField="AreaName" ItemStyle-HorizontalAlign="Center" />
					<asp:BoundField HeaderText="创建时间" DataField="CreateTime" DataFormatString="{0:yyyy-MM-dd}" ItemStyle-HorizontalAlign="Center" />
					<asp:BoundField HeaderText="创建人" DataField="Creator" ItemStyle-HorizontalAlign="Center" />
                    <asp:TemplateField HeaderText="记录总数">
						<ItemTemplate>
							<asp:Label ID="lblRecordCount" runat="server" Text=''></asp:Label>
						</ItemTemplate>
						<ItemStyle HorizontalAlign="Center" Width="80px" />
					</asp:TemplateField>
					<asp:TemplateField HeaderText="操作">
						<ItemTemplate>
							<asp:LinkButton ID="lbtnView" runat="server" CssClass="light btn" OnClientClick='<%# string.Format("View(\"{0}\"); return false;", Eval("Id")) %>' Text="查看详细"></asp:LinkButton>
							<asp:LinkButton ID="lbtnDelete" runat="server" CssClass=" light btnRed" 
								OnClientClick='<%# string.Format("return confirm(\"确定要删除该考勤记录吗？\");") %>' 
								OnCommand="lbtnDelete_Command" 
								CommandArgument='<%# Eval("Id") %>' 
								Text="删除"></asp:LinkButton>
						</ItemTemplate>
					</asp:TemplateField>
				</Columns>
				<RowStyle HorizontalAlign="Center"></RowStyle>
			</asp:GridView>
			<div id="pager" class="paging-bar">
				<div class="l-btns">
					<span>显示</span><asp:TextBox ID="txtPageNum" runat="server"
						AutoPostBack="True"
						CssClass="pagenum" onkeydown="return checkNumber(event);"
						OnTextChanged="txtPageNum_TextChanged"></asp:TextBox><span>条/页</span>
				</div>
				<webdiyer:AspNetPager ID="AspNetPager1" runat="server" AlwaysShow="True" AlwaysShowFirstLastPageNumber="True" CssClass="paging-default" CurrentPageButtonClass="current" CurrentPageButtonPosition="Center" CustomInfoHTML="&lt;span&gt;共%RecordCount%条记录&lt;/span&gt;" CustomInfoSectionWidth="" CustomInfoStyle="" CustomInfoTextAlign="NotSet" FirstPageText="首页" LastPageText="尾页" NavigationToolTipTextFormatString="{0}" NextPageText="下一页" NumericButtonCount="7" OnPageChanged="AspNetPager1_PageChanged" PageIndexBoxType="TextBox" PageSize="10" PagingButtonSpacing="0px" PrevPageText="上一页" ShowCustomInfoSection="Left" ShowFirstLast="False" ShowNavigationToolTip="True" ShowPageIndexBox="Never" UrlPaging="false">
				</webdiyer:AspNetPager>
			</div>
			<div class="footer-bar">
				<input type="button" id="btnAdd" class="btnGreen" value="导入考勤记录" runat="server" />
			</div>
		</div>
	</form>
	<script>
        var columnId = '<%=Area_Basic.CurrentColumnId %>';
        
		$(function () {
			$('#btnAdd').click(function () {
				Add();
			})
		})
		//新增
		function Add() {
			var index = layer.open({
				type: 2,
				title: '导入教师考勤记录',
				area: ['500px', '400px'],
				content: "/ECB.PC/custom_attendance_import.aspx?columnid=" + columnId,
				success: function (layero, index) {
				}
			});
		}

        function View(id) {
			var index = layer.open({
				type: 2,
				title: '查看考勤记录详细',
				content: "/ECB.PC/custom_attendance_view.aspx?id=" + id,
				area: ['80%', '80%'],
				maxmin: true,
				shadeClose:true,
				success: function (layero, index) {
				}
			});
		}
	</script>
</body>
</html>
